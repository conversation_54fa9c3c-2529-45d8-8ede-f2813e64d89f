import { getTranslation } from '@/lib/i18n';
import { <PERSON><PERSON>, <PERSON> } from '@heroui/react';

export default function CTA({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <div className='section text-center bg-gradient-to-b from-blue-50 to-white my-20'>
      <h2 className="text-2xl font-bold px-2 py-4">{t('Try Brat Generator Now!')}</h2>
      <p>{t("Experience the easiest way to create custom text images. No sign-up required - start designing instantly!")}</p>
      <Link href="/">
        <Button color='primary' className='mt-10'>{t('Try Brat Generator Now!')}</Button>
      </Link>
    </div>
  )
}
